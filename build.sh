#!/bin/bash

# ================================================
# Flutter Cross-Platform Build Script
# Supports: Mac, Android, iOS
# Environments: QA, DEV, PROD
# App: <PERSON>eazy Guest Kiosk
# ================================================

APP_NAME="Dineazy Guest Kiosk"
LOG_FILE="$(pwd)/build.log"
LOCAL_BUILD_PATH="localBuilds"

# Logging helper
log_message() {
    local message="$1"
    echo "$(date +"%Y-%m-%d %H:%M:%S") - $message" | tee -a "$LOG_FILE"
}

# Initialize clean build environment
init_environment() {
    if [ -d "$LOCAL_BUILD_PATH" ]; then
        log_message "Clearing existing build directory: $LOCAL_BUILD_PATH"
        rm -rf "$LOCAL_BUILD_PATH"
    fi

    if [ -f "$LOG_FILE" ]; then
        rm "$LOG_FILE"
    fi
    touch "$LOG_FILE"
}

# Detect OS
determine_os() {
    case "$(uname -s)" in
        Linux*)     OS="Linux";;
        Darwin*)    OS="Mac";;
        CYGWIN*|MINGW32*|MSYS*|MINGW*) OS="Windows";;
        *)          OS="Unknown";;
    esac
    log_message "Detected OS: $OS"
}

# Select environment (QA, DEV, PROD)
select_environments() {
    log_message "Selecting environments..."
    echo "Select environments (space-separated):"
    echo "1. QA"
    echo "2. DEV"
    echo "3. PROD"
    read -p "Enter choices (e.g., 1 3): " env_choices

    read -ra CHOICES <<< "$env_choices"
    ENVIRONMENTS=()

    for choice in "${CHOICES[@]}"; do
        case $choice in
            1) ENVIRONMENTS+=("qa") ;;
            2) ENVIRONMENTS+=("dev") ;;
            3) ENVIRONMENTS+=("prod") ;;
            *) log_message "Invalid selection: $choice. Skipping." ;;
        esac
    done

    if [ ${#ENVIRONMENTS[@]} -eq 0 ]; then
        log_message "No valid environments selected. Exiting."
        exit 1
    fi
    log_message "Selected environments: ${ENVIRONMENTS[*]}"
}

# Select build type
select_build_type() {
    log_message "Selecting build type..."
    echo "Select the build type:"
    echo "1. Android AppBundle (.aab)"
    echo "2. Android APK"
    echo "3. iOS IPA"
    echo "4. Android AppBundle + APK"
    echo "5. Android AppBundle + IPA"
    echo "6. APK + IPA"
    echo "7. All"
    read -p "Enter choice (1-7): " build_type_choice
}

# Bump Flutter version
bump_version() {
    CURRENT_VERSION=$(grep -E '^version: ' pubspec.yaml | awk '{print $2}')
    if [ -z "$CURRENT_VERSION" ]; then
        log_message "Error: Could not find the version in pubspec.yaml."
        exit 1
    fi

    VERSION_PART=$(echo "$CURRENT_VERSION" | cut -d '+' -f 1)
    BUILD_PART=$(echo "$CURRENT_VERSION" | cut -d '+' -f 2)
    IFS='.' read -r -a VERSION_ARRAY <<< "$VERSION_PART"

    PATCH_VERSION=${VERSION_ARRAY[2]}
    NEW_PATCH_VERSION=$((PATCH_VERSION + 1))
    NEW_BUILD_NUMBER=$((BUILD_PART + 1))
    NEW_VERSION="${VERSION_ARRAY[0]}.${VERSION_ARRAY[1]}.$NEW_PATCH_VERSION+$NEW_BUILD_NUMBER"

    sed -i.bak "s/^version: .*/version: $NEW_VERSION/" pubspec.yaml

    if grep -q "^version: $NEW_VERSION" pubspec.yaml; then
        log_message "Version bumped to $NEW_VERSION"
    else
        log_message "Error: Failed to update version."
        exit 1
    fi
}

# Build Flutter targets
run_build() {
    local build_type=$1
    local environment=$2
    ENV_FILE=".environment/$environment.json"

    if [ ! -f "$ENV_FILE" ]; then
        log_message "Missing environment file: $ENV_FILE"
        return
    fi

    BUILD_PATH="$LOCAL_BUILD_PATH/$environment"
    mkdir -p "$BUILD_PATH"

    bump_version
    flutter clean | tee -a "$LOG_FILE"
    flutter pub get | tee -a "$LOG_FILE"

    # Android AppBundle
    if [[ "$build_type" =~ ^(1|4|5|7)$ ]]; then
        log_message "Building AAB for $environment..."
        flutter build appbundle --release --dart-define-from-file="$ENV_FILE"
        mv build/app/outputs/bundle/release/app-release.aab "$BUILD_PATH/${APP_NAME// /_}.aab"
    fi

    # Android APK
    if [[ "$build_type" =~ ^(2|4|6|7)$ ]]; then
        log_message "Building APK for $environment..."
        flutter build apk --release --dart-define-from-file="$ENV_FILE"
        mv build/app/outputs/apk/release/app-release.apk "$BUILD_PATH/${APP_NAME// /_}.apk"
    fi

    # iOS IPA (Mac only)
    if [[ "$OS" == "Mac" && "$build_type" =~ ^(3|5|6|7)$ ]]; then
        log_message "Building IPA for $environment..."
        flutter build ipa --release --dart-define-from-file="$ENV_FILE"
        mv build/ios/ipa/*.ipa "$BUILD_PATH/${APP_NAME// /_}.ipa"
    fi
}

main() {
    init_environment
    determine_os
    select_environments
    select_build_type

    for ENVIRONMENT in "${ENVIRONMENTS[@]}"; do
        run_build "$build_type_choice" "$ENVIRONMENT"
    done

    log_message "All builds complete."
    echo "Check logs: $LOG_FILE"
}

main
