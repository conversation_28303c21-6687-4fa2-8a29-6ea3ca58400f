# ================================================
# Windows Flutter Build and Installer Script
# Supports Environments: QA, DEV, PROD
# App: Dineazy Guest Kiosk
# ================================================

$APP_NAME = "Dineazy Guest Kiosk"
$LOG_FILE = "$(Get-Location)\build_windows.log"
$LOCAL_BUILD_PATH = "$(Get-Location)\localBuilds"
$ICON_PATH = "$(Get-Location)\assets\logo\dineazy_logo.ico"
$script:NEW_VERSION = ""

# Logging helper
Function Log {
    param([string]$Message)
    $Timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    Write-Host "$Timestamp - $Message"
    Add-Content -Path $LOG_FILE -Value "$Timestamp - $Message" -ErrorAction SilentlyContinue
}

# Initialize clean build environment
Function Initialize-Environment {
    if (Test-Path $LOCAL_BUILD_PATH) {
        Log "Clearing existing build directory: $LOCAL_BUILD_PATH"
        Remove-Item $LOCAL_BUILD_PATH -Recurse -Force -ErrorAction SilentlyContinue
    }

    if (Test-Path $LOG_FILE) {
        Remove-Item $LOG_FILE -Force -ErrorAction SilentlyContinue
    }
    New-Item -ItemType File -Path $LOG_FILE -Force | Out-Null
    Log "Build environment initialized."
}

# Select environment (QA, DEV, PROD)
Function Select-Environments {
    Log "Selecting environments..."
    Write-Host "Select environments (space-separated):"
    Write-Host "1. QA"
    Write-Host "2. DEV"
    Write-Host "3. PROD"
    $envChoices = Read-Host "Enter choices (e.g., 1 3)"

    $choices = $envChoices.Split(" ", [System.StringSplitOptions]::RemoveEmptyEntries)
    $script:ENVIRONMENTS = @()

    foreach ($choice in $choices) {
        switch ($choice) {
            "1" { $script:ENVIRONMENTS += "qa" }
            "2" { $script:ENVIRONMENTS += "dev" }
            "3" { $script:ENVIRONMENTS += "prod" }
            default { Log "Invalid selection: $choice. Skipping." }
        }
    }

    if ($script:ENVIRONMENTS.Length -eq 0) {
        Log "No valid environments selected. Exiting."
        exit 1
    }
    Log "Selected environments: $($script:ENVIRONMENTS -join ', ')"
}

# Bump Flutter version
Function Bump-Version {
    $pubspec = Get-Content -Path "pubspec.yaml" -ErrorAction Stop
    $currentVersionLine = $pubspec | Where-Object { $_ -match "^version: " }
    if (-not $currentVersionLine) {
        Log "Error: Could not find the version in pubspec.yaml."
        exit 1
    }

    $currentVersion = $currentVersionLine.Split(" ")[1]
    if (-not $currentVersion -match "^\d+\.\d+\.\d+\+\d+$") {
        Log "Error: Invalid version format in pubspec.yaml: $currentVersion"
        exit 1
    }

    $versionPart, $buildPart = $currentVersion.Split("+")
    $versionArray = $versionPart.Split(".")
    $patchVersion = [int]$versionArray[2]
    $newPatchVersion = $patchVersion + 1
    $newBuildNumber = [int]$buildPart + 1
    $script:NEW_VERSION = "$($versionArray[0]).$($versionArray[1]).$newPatchVersion+$newBuildNumber"

    $pubspec = $pubspec -replace "^version: .*", "version: $script:NEW_VERSION"
    Set-Content -Path "pubspec.yaml" -Value $pubspec -Encoding UTF8 -ErrorAction Stop

    if ((Get-Content -Path "pubspec.yaml") -contains "version: $script:NEW_VERSION") {
        Log "Version bumped to $script:NEW_VERSION"
    } else {
        Log "Error: Failed to update version in pubspec.yaml."
        exit 1
    }
}

# Build Windows app for a specific environment
Function Run-Build {
    param([string]$Environment)
    $envFile = ".environment\$Environment.json"
    $buildPath = "$LOCAL_BUILD_PATH\$Environment"
    $installerName = "$($APP_NAME -replace ' ', '_')-$Environment-Setup"
    $iconRelativePath = "localBuilds\$Environment\dineazy_logo.ico"

    if (-not (Test-Path $envFile)) {
        Log "Missing environment file: $envFile"
        return
    }

    # Create build directory before copying icon
    New-Item -ItemType Directory -Force -Path $buildPath | Out-Null

    # Verify and copy icon file
    if (-not (Test-Path $ICON_PATH)) {
        Log "Error: Icon file not found at $ICON_PATH. Using default icon."
        $iconFilePath = ""
    } else {
        $iconFilePath = Join-Path $buildPath "dineazy_logo.ico"
        try {
            Copy-Item $ICON_PATH $iconFilePath -ErrorAction Stop
            Log "Icon copied to $iconFilePath"
        } catch {
            Log "Error: Failed to copy icon to $iconFilePath. Using default icon. Error: $_"
            $iconFilePath = ""
        }
    }

    Log "Building Windows app for $Environment..."

    Bump-Version
    flutter clean | Out-File -Append -FilePath $LOG_FILE
    flutter pub get | Out-File -Append -FilePath $LOG_FILE
    flutter build windows --release --dart-define-from-file="$envFile" | Out-File -Append -FilePath $LOG_FILE

    if (-not (Test-Path "build\windows\x64\runner\Release")) {
        Log "Error: Windows build failed for $Environment."
        return
    }

    Log "Copying build artifacts to $buildPath..."
    Copy-Item "build\windows\x64\runner\Release\*" $buildPath -Recurse -ErrorAction SilentlyContinue

    # Normalize icon path for Inno Setup (ensure backslashes)
    $issIconFilePath = if ($iconFilePath) { $iconFilePath -replace '/', '\\' } else { "" }

    # Create Inno Setup Script
    $ISS = @"
[Setup]
AppId={{773B341C-C0E8-4327-8515-B2C7233EDBC7}
AppName=$APP_NAME ($Environment)
AppVersion=$script:NEW_VERSION
AppPublisher=ITProfound, Inc.
AppPublisherURL=https://itprofound.com/
AppSupportURL=https://www.dineazy.com/
DefaultDirName={autopf}\$APP_NAME\$Environment
UninstallDisplayIcon={app}\$($APP_NAME -replace ' ', '_').exe
ArchitecturesAllowed=x64compatible
ArchitecturesInstallIn64BitMode=x64compatible
DisableProgramGroupPage=yes
OutputBaseFilename=$installerName
Compression=lzma
SolidCompression=yes
$(if ($iconFilePath) { "SetupIconFile=$($issIconFilePath -replace '/', '\\')" } else { "" })

[Languages]
Name: "english"; MessagesFile: "compiler:Default.isl"

[Tasks]
Name: "desktopicon"; Description: "{cm:CreateDesktopIcon}"; GroupDescription: "{cm:AdditionalIcons}"; Flags: unchecked

[Files]
Source: "$($buildPath -replace '/', '\\')\*"; DestDir: "{app}"; Flags: ignoreversion recursesubdirs createallsubdirs

[Icons]
Name: "{autoprograms}\$APP_NAME ($Environment)"; Filename: "{app}\$($APP_NAME -replace ' ', '_').exe"
Name: "{autodesktop}\$APP_NAME ($Environment)"; Filename: "{app}\$($APP_NAME -replace ' ', '_').exe"; Tasks: desktopicon

[Run]
Filename: "{app}\$($APP_NAME -replace ' ', '_').exe"; Description: "{cm:LaunchProgram,$($APP_NAME -replace ' ', '_')}"; Flags: nowait postinstall skipifsilent
"@
    $issPath = "$PWD\installer_script_$Environment.iss"
    # Write ISS file with UTF-8 encoding (no BOM)
    [IO.File]::WriteAllText($issPath, $ISS)
    Log "Generated Inno Setup script at $issPath"
    # Log the actual file content after writing
    $issContent = Get-Content -Path $issPath -Raw
    Log "Inno Setup script content (from file):`n$issContent"

    # Run Inno Setup Compiler
    Log "Creating installer for $Environment..."
    try {
        iscc $issPath | Out-File -Append -FilePath $LOG_FILE
        if ($LASTEXITCODE -eq 0) {
            Log "Windows installer for $Environment built successfully!"
        } else {
            Log "Error: Inno Setup compilation failed for $Environment."
        }
    } catch {
        Log "Error: Failed to run Inno Setup compiler: $_"
    }
}

# Main function
Function Main {
    Initialize-Environment
    Select-Environments

    foreach ($env in $script:ENVIRONMENTS) {
        Run-Build -Environment $env
    }

    Log "All builds complete."
    Write-Host "Check logs: $LOG_FILE"
}

# Execute main
Main