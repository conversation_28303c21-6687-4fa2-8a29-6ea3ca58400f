import 'dart:async';
import 'dart:math';
import 'package:flutter/material.dart';

import '../models/base.dart';

/// Utility functions for the app

/// Generate a random password
String getNewPassword() {
  const String chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#\$%^&*';
  Random random = Random();
  return String.fromCharCodes(
    Iterable.generate(12, (_) => chars.codeUnitAt(random.nextInt(chars.length))),
  );
}

/// Get current country dial code (default implementation)
String getCurrentCountryDialCode() {
  // Default to US dial code, you can implement proper country detection
  return '+1';
}

/// Show success snackbar
void showSnackBar(BuildContext context, String message) {
  if (!context.mounted) return;
  
  ScaffoldMessenger.of(context).showSnackBar(
    SnackBar(
      content: Text(message),
      backgroundColor: Colors.green,
      behavior: SnackBarBehavior.floating,
      duration: const Duration(seconds: 3),
    ),
  );
}

/// Show error snackbar
void showErrorSnackBar(BuildContext context, String message) {
  if (!context.mounted) return;
  
  ScaffoldMessenger.of(context).showSnackBar(
    SnackBar(
      content: Text(message),
      backgroundColor: Colors.red,
      behavior: SnackBarBehavior.floating,
      duration: const Duration(seconds: 4),
    ),
  );
}

/// Show warning snackbar
void showWarningSnackBar(BuildContext context, String message) {
  if (!context.mounted) return;
  
  ScaffoldMessenger.of(context).showSnackBar(
    SnackBar(
      content: Text(message),
      backgroundColor: Colors.orange,
      behavior: SnackBarBehavior.floating,
      duration: const Duration(seconds: 3),
    ),
  );
}

/// Format phone number for display
String formatPhoneNumber(String phone, String? countryCode) {
  if (countryCode != null && phone.isNotEmpty) {
    return '$countryCode $phone';
  }
  return phone;
}

/// Validate email format
bool isValidEmail(String email) {
  return RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(email);
}

/// Validate phone number format
bool isValidPhoneNumber(String phone) {
  return RegExp(r'^\d{10,15}$').hasMatch(phone.replaceAll(RegExp(r'[^\d]'), ''));
}

/// Capitalize first letter of each word
String capitalizeWords(String text) {
  return text.split(' ').map((word) {
    if (word.isEmpty) return word;
    return word[0].toUpperCase() + word.substring(1).toLowerCase();
  }).join(' ');
}

/// Format currency amount with restaurant currency symbol
String formatCurrency(double amount, {String? symbol}) {
  // Default symbol if none provided
  symbol ??= '\$';
  return '$symbol${amount.toStringAsFixed(2)}';
}

/// Get currency symbol from restaurant settings
String getCurrencySymbol(String? currencyId) {
  if (currencyId == null || currencyId.isEmpty) {
    return '\$'; // Default to USD symbol
  }

  try {
    final currency = CurrencyValues.byId(currencyId);
    return currency.symbol;
  } catch (e) {
    debugPrint('getCurrencySymbol: Error getting currency symbol for ID $currencyId: $e');
    return '\$'; // Fallback to USD symbol
  }
}

/// Format currency with restaurant currency symbol
String formatCurrencyWithRestaurant(double amount, String? currencyId) {
  final symbol = getCurrencySymbol(currencyId);
  return formatCurrency(amount, symbol: symbol);
}

/// Get initials from name
String getInitials(String name) {
  List<String> names = name.trim().split(' ');
  if (names.isEmpty) return '';
  if (names.length == 1) return names[0][0].toUpperCase();
  return '${names[0][0]}${names[names.length - 1][0]}'.toUpperCase();
}

/// Debounce function for search
class Debouncer {
  final int milliseconds;
  Timer? _timer;

  Debouncer({required this.milliseconds});

  void run(VoidCallback action) {
    _timer?.cancel();
    _timer = Timer(Duration(milliseconds: milliseconds), action);
  }

  void dispose() {
    _timer?.cancel();
  }
}


