import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

import '../../common/constants/constants.dart';
import '../../models/order/order.dart';
import '../../models/payment_request_details.dart';
import '../../models/restaurant/restaurant.dart';
import '../../screens/cart/cart_screen.dart';
import '../../screens/home/<USER>';
import '../../screens/menu/menu_screen.dart';
import '../../screens/order/order_confirmation_screen.dart';
import '../../screens/payments/payment_failed_screen.dart';
import '../../screens/payments/payment_webview_screen.dart';
import '../../screens/auth/login_screen.dart';

BuildContext? get globalNavigatorState => globalNavigatorKey.currentContext;

GoRouter getRouterConfig(String initialRoute) {
  return GoRouter(
    initialLocation: initialRoute,
    navigatorKey: globalNavigatorKey,
    routes: [
      GoRoute(path: '/', builder: (context, state) => const LoginScreen()),
      GoRoute(path: '/home', builder: (context, state) => const HomeScreen()),
      GoRoute(path: '/menu', builder: (context, state) => const MenuScreen()),
      GoRoute(path: '/cart', builder: (context, state) => const CartDetailsScreen()),

      // Payment routes
      GoRoute(
        path: '/payment-webview',
        builder: (context, state) {
          final extra = state.extra as Map<String, dynamic>;
          return PaymentWebViewScreen(
            paymentDetails: extra['paymentDetails'] as PaymentRequestDetails,
            restaurant: extra['restaurant'] as Restaurant,
            order: extra['order'] as Order,
          );
        },
      ),

      // Order confirmation route (for successful orders)
      GoRoute(
        path: '/order-confirmation',
        builder: (context, state) {
          final order = state.extra as Order;
          return OrderConfirmationScreen(order: order);
        },
      ),

      // Payment failed route
      GoRoute(
        path: '/payment-failed',
        builder: (context, state) {
          final errorMessage = state.extra as String?;
          return PaymentFailedScreen(errorMessage: errorMessage);
        },
      ),
    ],
  );
}
