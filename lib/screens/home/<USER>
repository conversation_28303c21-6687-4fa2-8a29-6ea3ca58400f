import 'dart:math' as math;

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';

import '../../common/constants/colors.dart';
import '../../core/utils.dart';
import '../../models/order/order_type.dart';
import '../../providers/auth_provider.dart';
import '../../providers/data_provider.dart';
import '../../widgets/loading_dialog.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> with TickerProviderStateMixin {
  late AnimationController _floatingController;
  late AnimationController _pulseController;
  late Animation<double> _floatingAnimation;
  late Animation<double> _pulseAnimation;
  bool _isInitializing = false;

  @override
  void initState() {
    super.initState();
    _floatingController = AnimationController(duration: const Duration(seconds: 3), vsync: this)..repeat(reverse: true);

    _pulseController = AnimationController(duration: const Duration(seconds: 2), vsync: this)..repeat(reverse: true);

    _floatingAnimation = Tween<double>(
      begin: -10,
      end: 10,
    ).animate(CurvedAnimation(parent: _floatingController, curve: Curves.easeInOut));

    _pulseAnimation = Tween<double>(
      begin: 1.0,
      end: 1.05,
    ).animate(CurvedAnimation(parent: _pulseController, curve: Curves.easeInOut));

    // Initialize session if coming from persistent login
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _initializeSessionIfNeeded();
    });
  }

  Future<void> _initializeSessionIfNeeded() async {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    final dataProvider = Provider.of<DataProvider>(context, listen: false);

    // If user has valid session but restaurant not loaded, initialize
    if (authProvider.hasValidSession() && !dataProvider.hasRestaurant && !_isInitializing) {
      setState(() {
        _isInitializing = true;
      });

      await authProvider.initializeSession(context);

      setState(() {
        _isInitializing = false;
      });
    }
  }

  Future<void> _loadMenuAndNavigate(BuildContext context) async {
    final dataProvider = Provider.of<DataProvider>(context, listen: false);

    if (!dataProvider.hasRestaurant) {
      debugPrint('HomeScreen._loadMenuAndNavigate: Restaurant not loaded');
      return;
    }

    // Show loading dialog
    LoadingDialog? loadingDialog;
    if (mounted) {
      loadingDialog = LoadingDialog(context, 'Loading menu...');
      loadingDialog.show();
    }

    try {
      await dataProvider.loadMenu(dataProvider.restaurant, OrderType.takeaway);

      if (!dataProvider.isMenuLoading) {
        loadingDialog?.dismiss();
        if (context.mounted) {
          context.push('/menu');
        }
      }
    } catch (e) {
      loadingDialog?.dismiss();
      debugPrint('HomeScreen._loadMenuAndNavigate: Error loading menu: $e');

      // Show error message
      if (context.mounted) {
        showErrorSnackBar(context, 'Failed to load menu: $e');
      }
    }
  }

  @override
  void dispose() {
    _floatingController.dispose();
    _pulseController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    final DataProvider dataProvider = Provider.of<DataProvider>(context);

    final restaurantDetails = dataProvider.restaurantInfo;

    return Scaffold(
      body: Container(
        width: double.infinity,
        height: double.infinity,
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [AppColors.gradientStart, AppColors.gradientMiddle, AppColors.gradientEnd],
          ),
        ),
        child: Stack(
          children: [
            ...List.generate(6, (index) {
              return AnimatedBuilder(
                animation: _floatingController,
                builder: (context, child) {
                  final angle = (index * 60.0) + (_floatingController.value * 30);

                  final foodIcons = [
                    Icons.restaurant,
                    Icons.cake,
                    Icons.local_drink,
                    Icons.fastfood,
                    Icons.local_pizza,
                    Icons.local_cafe,
                  ];
                  final icon = foodIcons[index % foodIcons.length];

                  return Positioned(
                    left: size.width * 0.1 + (index * size.width * 0.15),
                    top: size.height * 0.2 + (math.sin(angle * math.pi / 180) * 50),
                    child: Transform.rotate(
                      angle: angle * math.pi / 180,
                      child: Icon(icon, size: 40 + (index * 10), color: AppColors.lightGrey.withAlpha(100)),
                    ),
                  );
                },
              );
            }),

            // Main Content
            SafeArea(
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 40, vertical: 20),
                child: Column(
                  children: [
                    // Header Section
                    SizedBox(
                      height: 80,
                      child: Row(
                        children: [
                          Container(
                            width: 80,
                            height: 80,
                            decoration: BoxDecoration(
                              color: AppColors.lightGrey.withAlpha(60),
                              borderRadius: BorderRadius.circular(20),
                              border: Border.all(color: AppColors.white.withAlpha(20)),
                            ),
                            child: ClipRRect(
                              borderRadius: BorderRadius.circular(18),
                              child: restaurantDetails.logoUrl != null
                                  ? CachedNetworkImage(
                                      imageUrl: restaurantDetails.logoUrl!,
                                      width: 80,
                                      height: 80,
                                      fit: BoxFit.cover,
                                      placeholder: (context, url) => Container(
                                        padding: const EdgeInsets.all(8),
                                        child: const Icon(Icons.restaurant, color: Colors.white, size: 40),
                                      ),
                                      errorWidget: (context, url, error) => Container(
                                        padding: const EdgeInsets.all(8),
                                        child: const Icon(Icons.restaurant, color: Colors.white, size: 40),
                                      ),
                                    )
                                  : Container(
                                      padding: const EdgeInsets.all(8),
                                      child: const Icon(Icons.restaurant, color: Colors.white, size: 40),
                                    ),
                            ),
                          ),
                          const SizedBox(width: 24),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Text(
                                  restaurantDetails.name,
                                  style: const TextStyle(
                                    color: Colors.white,
                                    fontSize: 28,
                                    fontWeight: FontWeight.w800,
                                    letterSpacing: -0.5,
                                  ),
                                ),
                                const SizedBox(height: 4),
                                Text(
                                  'Self-Service Ordering',
                                  style: TextStyle(
                                    color: AppColors.white.withAlpha(200),
                                    fontSize: 16,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),

                    // Hero Section
                    Expanded(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Flexible(
                            child: AnimatedBuilder(
                              animation: _floatingAnimation,
                              builder: (context, child) {
                                return Transform.translate(
                                  offset: Offset(0, _floatingAnimation.value),
                                  child: Container(
                                    width: 120,
                                    height: 120,
                                    decoration: BoxDecoration(
                                      shape: BoxShape.circle,
                                      color: AppColors.lightGrey.withAlpha(65),
                                      border: Border.all(color: AppColors.lightGrey.withAlpha(30), width: 3),
                                      boxShadow: [
                                        BoxShadow(
                                          color: AppColors.white.withAlpha(10),
                                          offset: const Offset(0, 20),
                                          blurRadius: 40,
                                        ),
                                      ],
                                    ),
                                    child: const Icon(Icons.touch_app, size: 48, color: AppColors.white),
                                  ),
                                );
                              },
                            ),
                          ),

                          const SizedBox(height: 16),

                          const Text(
                            'Welcome to',
                            style: TextStyle(color: AppColors.white, fontSize: 18, fontWeight: FontWeight.w400),
                          ),

                          const SizedBox(height: 8),

                          const Text(
                            'Digital Dining',
                            style: TextStyle(
                              color: AppColors.white,
                              fontSize: 48,
                              fontWeight: FontWeight.w900,
                              letterSpacing: -2,
                              height: 1.1,
                            ),
                          ),

                          const SizedBox(height: 16),

                          Container(
                            padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
                            decoration: BoxDecoration(
                              color: AppColors.lightGrey.withAlpha(50),
                              borderRadius: BorderRadius.circular(30),
                              border: Border.all(color: AppColors.lightGrey.withAlpha(10)),
                            ),
                            child: Text(
                              'Order fresh food in seconds',
                              style: TextStyle(
                                color: AppColors.white.withAlpha(200),
                                fontSize: 14,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),

                          const SizedBox(height: 24),

                          // Action Buttons
                          AnimatedBuilder(
                            animation: _pulseAnimation,
                            builder: (context, child) {
                              return Transform.scale(
                                scale: _pulseAnimation.value,
                                child: Container(
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(24),
                                    boxShadow: [
                                      BoxShadow(
                                        color: Colors.white.withAlpha(30),
                                        offset: const Offset(0, 8),
                                        blurRadius: 24,
                                        spreadRadius: 0,
                                      ),
                                    ],
                                  ),
                                  child: ElevatedButton(
                                    onPressed: () async {
                                      await _loadMenuAndNavigate(context);
                                    },
                                    style: ElevatedButton.styleFrom(
                                      backgroundColor: Colors.white,
                                      foregroundColor: AppColors.filterSelected,
                                      elevation: 0,
                                      padding: const EdgeInsets.symmetric(horizontal: 48, vertical: 20),
                                      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
                                      minimumSize: const Size(240, 64),
                                    ),
                                    child: const Row(
                                      mainAxisSize: MainAxisSize.min,
                                      children: [
                                        Icon(Icons.restaurant_menu, size: 28),
                                        SizedBox(width: 12),
                                        Text(
                                          'Browse Menu',
                                          style: TextStyle(fontSize: 20, fontWeight: FontWeight.w700),
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                              );
                            },
                          ),
                        ],
                      ),
                    ),

                    // Features Section
                    Container(
                      height: 180,
                      padding: const EdgeInsets.all(16),
                      margin: const EdgeInsets.only(top: 16, bottom: 16),
                      decoration: BoxDecoration(
                        color: AppColors.lightGrey.withAlpha(20),
                        borderRadius: BorderRadius.circular(20),
                        border: Border.all(color: AppColors.lightGrey.withAlpha(10)),
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                        children: [
                          _buildFeature(Icons.speed, 'Fast Service', 'Order in under 2 minutes'),
                          Container(width: 1, height: 30, color: AppColors.lightGrey),
                          _buildFeature(Icons.payment, 'Easy Payment', 'Card, cash, or mobile pay'),
                          Container(width: 1, height: 30, color: AppColors.lightGrey),
                          _buildFeature(Icons.queue, 'No Waiting', 'No waiting in line'),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFeature(IconData icon, String title, String description) {
    return Expanded(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            padding: const EdgeInsets.all(10),
            decoration: BoxDecoration(color: AppColors.white, borderRadius: BorderRadius.circular(16)),
            child: Icon(icon, color: AppColors.textTertiary, size: 36),
          ),
          const SizedBox(height: 8),
          Text(
            title,
            style: const TextStyle(color: AppColors.white, fontSize: 18, fontWeight: FontWeight.w700),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 4),
          Text(
            description,
            style: TextStyle(color: AppColors.white.withAlpha(200), fontSize: 14, fontWeight: FontWeight.w500),
            textAlign: TextAlign.center,
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }
}
