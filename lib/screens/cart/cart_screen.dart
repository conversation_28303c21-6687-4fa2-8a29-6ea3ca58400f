import 'package:dineazy_guest_kiosk/providers/cart_provider.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';

import '../../common/constants/colors.dart';
import '../../core/utils.dart';
import '../../models/order/order_type.dart';
import '../../providers/data_provider.dart';
import 'widgets/cart_item_card.dart';

class CartDetailsScreen extends StatefulWidget {
  const CartDetailsScreen({super.key});

  @override
  State<CartDetailsScreen> createState() => _CartDetailsScreenState();
}

class _CartDetailsScreenState extends State<CartDetailsScreen> with TickerProviderStateMixin {
  late AnimationController _animationController;

  // late Animation<double> _scaleAnimation;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(duration: const Duration(milliseconds: 400), vsync: this);

    // _scaleAnimation = Tween<double>(begin: 0.8, end: 1.0).animate(CurvedAnimation(parent: _animationController, curve: Curves.elasticOut));

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(CurvedAnimation(parent: _animationController, curve: Curves.easeInOut));

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void checkout() {
    final CartProvider cartProvider = Provider.of<CartProvider>(context, listen: false);

    // Set order type to takeaway if not already set
    if (cartProvider.orderType == null) {
      cartProvider.setOrderType(OrderType.takeaway);
    }

    // Process Stripe payment
    cartProvider.processStripePayment(context);
  }

  @override
  Widget build(BuildContext context) {
    final CartProvider cartProvider = Provider.of<CartProvider>(context);
    final cartItems = cartProvider.cartItems;
    final totalPrice = cartProvider.totalPrice;

    return Scaffold(
      backgroundColor: AppColors.surfaceLight,
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [AppColors.primary.withAlpha(8), AppColors.surfaceLight, Colors.white]
          ),
        ),
        child: SafeArea(
          child: AnimatedBuilder(
            animation: _animationController,
            builder: (context, child) {
              return FadeTransition(
                opacity: _fadeAnimation,
                child: Column(
                  children: [
                    // Enhanced App Bar Section
                    Container(
                      padding: const EdgeInsets.all(24),
                      child: Row(
                        children: [
                          // Enhanced Back Button
                          Container(
                            decoration: BoxDecoration(
                              gradient: AppColors.buttonGradient,
                              borderRadius: BorderRadius.circular(20),
                              boxShadow: AppColors.elevatedShadow,
                            ),
                            child: Material(
                              color: Colors.transparent,
                              child: InkWell(
                                onTap: () => context.pop(),
                                borderRadius: BorderRadius.circular(20),
                                child: Container(
                                  padding: const EdgeInsets.all(16),
                                  child: const Icon(
                                    Icons.arrow_back_ios_new_rounded,
                                    color: Colors.white,
                                    size: 24
                                  ),
                                ),
                              ),
                            ),
                          ),
                          const SizedBox(width: 20),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                const Text(
                                  "Your Cart",
                                  style: TextStyle(
                                    fontSize: 32,
                                    fontWeight: FontWeight.bold,
                                    color: AppColors.textPrimary,
                                    letterSpacing: -0.5
                                  ),
                                ),
                                if (cartItems.isNotEmpty)
                                  Text(
                                    "${cartItems.length} item${cartItems.length > 1 ? 's' : ''} in cart",
                                    style: const TextStyle(
                                      fontSize: 16,
                                      color: AppColors.textSecondary,
                                      fontWeight: FontWeight.w500
                                    ),
                                  ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),

                    // Main Content Area - Two Column Layout
                    Expanded(
                      child: cartItems.isEmpty
                        ? _buildEmptyCartState()
                        : _buildTwoColumnLayout(cartItems, cartProvider, totalPrice),
                    ),
                  ],
                ),
              );
            },
          ),
        ),
      ),
    );
  }

  Widget _buildEmptyCartState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            padding: const EdgeInsets.all(32),
            decoration: BoxDecoration(
              color: AppColors.lightGrey,
              shape: BoxShape.circle,
              boxShadow: [BoxShadow(color: Colors.black.withAlpha(1), blurRadius: 20, offset: const Offset(0, 8))],
            ),
            child: Icon(Icons.shopping_cart_outlined, size: 80, color: AppColors.black.withAlpha(50)),
          ),
          const SizedBox(height: 32),
          Text(
            "Your cart is empty",
            style: TextStyle(fontSize: 32, fontWeight: FontWeight.bold, color: AppColors.black),
          ),
          const SizedBox(height: 12),
          Text(
            "Add some delicious items to get started",
            style: TextStyle(fontSize: 24, color: AppColors.primary),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 40),
          GestureDetector(
            onTap: () {
              context.go('/');
            },
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
              decoration: BoxDecoration(
                color: AppColors.primary.withAlpha(15),
                borderRadius: BorderRadius.circular(25),
                border: Border.all(color: AppColors.primary.withAlpha(2)),
              ),
              child: Text(
                "Browse Menu",
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600, color: AppColors.primary),
              ),
            ),
          ),
        ],
      ),
    );
  }

  // New Two Column Layout
  Widget _buildTwoColumnLayout(Map cartItems, CartProvider cartProvider, double totalPrice) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 24),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Left Column - Cart Items (60% width)
          Expanded(
            flex: 6,
            child: _buildCartItemsList(cartItems, cartProvider),
          ),

          const SizedBox(width: 24),

          // Right Column - Amount Breakdown (40% width)
          Expanded(
            flex: 4,
            child: _buildAmountBreakdownCard(totalPrice, cartProvider),
          ),
        ],
      ),
    );
  }

  Widget _buildCartItemsList(Map cartItems, CartProvider cartProvider) {

    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(24),
        boxShadow: AppColors.cardShadow,
        border: Border.all(color: AppColors.cardBorder, width: 1),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Container(
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              color: AppColors.surfaceLight,
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(24),
                topRight: Radius.circular(24),
              ),
              border: Border(
                bottom: BorderSide(color: AppColors.cardBorder, width: 1),
              ),
            ),
            child: Row(
              children: [
                Icon(Icons.shopping_cart_outlined, color: AppColors.primary, size: 24),
                const SizedBox(width: 12),
                const Text(
                  "Order Items",
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: AppColors.textPrimary,
                  ),
                ),
              ],
            ),
          ),

          // Items List
          Expanded(
            child: ListView.separated(
              physics: const BouncingScrollPhysics(),
              padding: const EdgeInsets.all(20),
              itemCount: cartItems.length,
              separatorBuilder: (_, __) => Container(
                height: 1,
                margin: const EdgeInsets.symmetric(vertical: 16),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [Colors.transparent, AppColors.cardBorder, Colors.transparent]
                  )
                ),
              ),
              itemBuilder: (context, index) {
                final itemHash = cartItems.keys.elementAt(index);
                final cartItem = cartItems[itemHash]!["cartItem"] as CartItem;
                return AnimatedContainer(
                  duration: Duration(milliseconds: 300 + (index * 100)),
                  curve: Curves.easeOutBack,
                  child: CartDetailsItem(
                    cartItem: cartItem,
                    onRemove: () => cartProvider.removeCartItem(itemHash),
                    onAdd: () => cartProvider.addItemByHash(itemHash),
                    onSubtract: () => cartProvider.removeItemByHash(itemHash),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  // New Amount Breakdown Card for Right Column
  Widget _buildAmountBreakdownCard(double totalPrice, CartProvider cartProvider) {
    final DataProvider dataProvider = Provider.of<DataProvider>(context);
    final subtotal = cartProvider.total;
    final taxAmount = cartProvider.tax;

    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(24),
        boxShadow: AppColors.cardShadow,
        border: Border.all(color: AppColors.cardBorder, width: 1),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Container(
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              gradient: AppColors.primaryGradient,
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(24),
                topRight: Radius.circular(24),
              ),
            ),
            child: const Row(
              children: [
                Icon(Icons.receipt_long_outlined, color: Colors.white, size: 24),
                SizedBox(width: 12),
                Text(
                  "Order Summary",
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
              ],
            ),
          ),

          // Amount Breakdown
          Expanded(
            child: Padding(
              padding: const EdgeInsets.all(24),
              child: Column(
                children: [
                  // Subtotal
                  _buildAmountRow("Subtotal", subtotal, dataProvider, false),

                  const SizedBox(height: 16),

                  // Tax (if applicable)
                  if (taxAmount > 0) ...[
                    _buildAmountRow("Tax", taxAmount, dataProvider, false),
                    const SizedBox(height: 16),
                  ],

                  // Divider
                  Container(
                    height: 1,
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: [Colors.transparent, AppColors.cardBorder, Colors.transparent]
                      )
                    ),
                  ),

                  const SizedBox(height: 20),

                  // Total
                  _buildAmountRow("Total Amount", totalPrice, dataProvider, true),

                  const Spacer(),

                  // Tax Info
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: AppColors.surfaceLight,
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(color: AppColors.cardBorder, width: 1),
                    ),
                    child: Row(
                      children: [
                        Icon(
                          taxAmount > 0 ? Icons.info_outline : Icons.check_circle_outline,
                          color: taxAmount > 0 ? AppColors.warning : AppColors.success,
                          size: 20,
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            taxAmount > 0 ? "Including all taxes" : "No taxes applicable",
                            style: TextStyle(
                              fontSize: 14,
                              color: AppColors.textSecondary,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),

                  const SizedBox(height: 24),

                  // Checkout Button
                  _buildEnhancedCheckoutButton(totalPrice, dataProvider),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAmountRow(String label, double amount, DataProvider dataProvider, bool isTotal) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: isTotal ? 18 : 16,
            fontWeight: isTotal ? FontWeight.bold : FontWeight.w500,
            color: isTotal ? AppColors.textPrimary : AppColors.textSecondary,
          ),
        ),
        Text(
          formatCurrencyWithRestaurant(
            amount,
            dataProvider.hasRestaurant ? dataProvider.restaurant.restaurantSettings.currencyValue : null
          ),
          style: TextStyle(
            fontSize: isTotal ? 20 : 16,
            fontWeight: isTotal ? FontWeight.bold : FontWeight.w600,
            color: isTotal ? AppColors.primary : AppColors.textPrimary,
          ),
        ),
      ],
    );
  }

  Widget _buildEnhancedCheckoutButton(double totalPrice, DataProvider dataProvider) {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        gradient: AppColors.buttonGradient,
        borderRadius: BorderRadius.circular(16),
        boxShadow: AppColors.buttonShadow,
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: checkout,
          borderRadius: BorderRadius.circular(16),
          child: Container(
            padding: const EdgeInsets.symmetric(vertical: 18),
            child: Column(
              children: [
                const Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(Icons.payment_rounded, color: Colors.white, size: 24),
                    SizedBox(width: 12),
                    Text(
                      "Proceed to Payment",
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 4),
                Text(
                  formatCurrencyWithRestaurant(
                    totalPrice,
                    dataProvider.hasRestaurant ? dataProvider.restaurant.restaurantSettings.currencyValue : null
                  ),
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }


}
