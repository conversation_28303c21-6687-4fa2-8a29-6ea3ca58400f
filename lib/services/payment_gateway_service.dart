import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

import '../api/api_manager.dart';
import '../common/types.dart';
import '../models/base.dart';
import '../models/order/order.dart';
import '../models/payment_request_details.dart';
import '../models/restaurant/restaurant.dart';

class PaymentGatewayService {
  final BuildContext context;

  PaymentGatewayService(this.context);

  /// Check if restaurant uses Stripe payment gateway
  Future<bool> isStripePaymentGateway(Restaurant restaurant) async {
    try {
      // Get the payment gateway domain values
      final ApiManager apiManager = ApiManager.tokenInstance();
      final paymentGatewayDomainValues = await apiManager.getPaymentGatewayDomainValues(restaurant.companyId);

      // Find the domain value that matches the restaurant's payment gateway ID
      final paymentGatewayId = restaurant.paymentSettings.paymentGatewayId;

      for (final domainValue in paymentGatewayDomainValues) {
        if (domainValue['_id'] == paymentGatewayId) {
          final gatewayName = (domainValue['name'] as String).toLowerCase();

          debugPrint('🐞 PaymentGatewayService.isStripePaymentGateway: gatewayName: $gatewayName');
          // Check if the gateway name contains 'stripe'
          return gatewayName.contains('stripe');
        }
      }

      debugPrint(
        'PaymentGatewayService.isStripePaymentGateway: Payment gateway ID $paymentGatewayId not found in domain values',
      );
      return false;
    } catch (e) {
      debugPrint('PaymentGatewayService.isStripePaymentGateway: Error checking payment gateway: $e');
      return false;
    }
  }

  /// Start payment process for takeaway orders
  Future<void> startTakeawayPayment({
    required Order order,
    required Restaurant restaurant,
    required VoidCallback onSuccess,
    required Function(String error) onFailed,
  }) async {
    try {
      // Check if restaurant uses Stripe
      if (!(await isStripePaymentGateway(restaurant))) {
        onFailed('Payment gateway not supported');
        return;
      }

      // Create payment request
      final paymentRequest = _createPaymentRequest(order, restaurant);

      // Get payment URL from backend
      final ApiManager apiManager = ApiManager.tokenInstance();
      debugPrint('PaymentGatewayService: Sending payment request: $paymentRequest');
      final PaymentRequestDetails paymentDetails = await apiManager.getPaymentUrl(restaurant, paymentRequest);
      debugPrint('PaymentGatewayService: Received payment details: $paymentDetails');

      // Navigate to payment webview with payment details
      if (context.mounted) {
        final result = await context.push(
          '/payment-webview',
          extra: {'paymentDetails': paymentDetails, 'restaurant': restaurant, 'order': order},
        );

        // Handle payment result
        if (result == 'success') {
          onSuccess();
        } else if (result == 'failed') {
          onFailed('Payment was cancelled or failed');
        }
      }
    } catch (e) {
      debugPrint('PaymentGatewayService.startTakeawayPayment: Error: $e');
      onFailed('Failed to initiate payment: $e');
    }
  }

  /// Create payment request object
  JSONObject _createPaymentRequest(Order order, Restaurant restaurant) {
    return {
      'orderId': order.id,
      'amount': double.parse(order.grandTotal.toStringAsFixed(2)),
      'currency': CurrencyValues.byId(restaurant.restaurantSettings.currencyValue).name,
      'paymentMethod': 'UPI',
      "paymentType": "ONLINE",
      "reference": "",
      "transactionNumber": "",
    };

  }

  /// Verify payment after successful completion
  Future<bool> verifyPayment({
    required PaymentRequestDetails paymentDetails,
    required Restaurant restaurant,
    required String paymentIntentId,
  }) async {
    try {
      final ApiManager apiManager = ApiManager.tokenInstance();

      final verificationBody =  {"id": paymentIntentId, "provider": "Stripe"};

      final verifiedPayment = await apiManager.verifyPayment(
        restaurant,
        paymentDetails,
        verificationBody,
        true
      );


      final isSuccess = verifiedPayment.status == 'succeeded' ||
                       verifiedPayment.status == 'paid' ||
                       verifiedPayment.status == 'PENDING' ||
                       verifiedPayment.status == 'COMPLETED';

      debugPrint('PaymentGatewayService.verifyPayment: Verification result: $isSuccess');
      return isSuccess;
    } catch (e) {
      debugPrint('PaymentGatewayService.verifyPayment: Error: $e');
      return false;
    }
  }
}
