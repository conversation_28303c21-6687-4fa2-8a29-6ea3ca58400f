import 'package:flutter/material.dart';
import '../constants/colors.dart';

class AppTheme {
  static ThemeData get theme {
    return ThemeData(
      scaffoldBackgroundColor: AppColors.transparent,
      primaryColor: AppColors.primary,

      colorScheme: ColorScheme.fromSeed(
        seedColor: AppColors.primary,
        secondary: AppColors.secondary,
      ),

      // Text styles
      textTheme: const TextTheme(
        bodyMedium: TextStyle(fontSize: 40 , fontWeight: FontWeight.bold,color: AppColors.white),
        bodyLarge: TextStyle(fontSize: 80, fontWeight: FontWeight.bold,color: AppColors.white),
        titleLarge: TextStyle(fontSize: 100, fontWeight: FontWeight.bold,color: AppColors.white),
      ),

      // Button style
        elevatedButtonTheme: ElevatedButtonThemeData(
          style: ElevatedButton.styleFrom(
            foregroundColor: AppColors.white,
            backgroundColor: AppColors.primary,
            padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 20),
            minimumSize: const Size(200, 60),
            textStyle: const TextStyle(
              fontSize: 40,
              fontWeight: FontWeight.bold,
              color: AppColors.white,
            ),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
          ),
        ),

    );
  }
  static BoxDecoration get itemCardDecoration {
    return BoxDecoration(
      color: AppColors.black.withValues(alpha: 0.4),
      borderRadius: BorderRadius.circular(20),
    );

  }
  static BoxDecoration get bottomBarDecoration {
    return BoxDecoration(
      color: Colors.teal,
      boxShadow: [
        BoxShadow(
          color: Colors.black.withValues(alpha: 0.15),
          blurRadius: 10,
          offset: const Offset(0, 4),
        ),
      ],
    );
  }

  static ButtonStyle get checkoutButtonStyle {
    return ElevatedButton.styleFrom(
      backgroundColor: Colors.green,
      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      textStyle: const TextStyle(
        fontSize: 18,
        fontWeight: FontWeight.bold,
      ),
    );
  }
}
