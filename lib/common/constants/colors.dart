import 'package:flutter/material.dart';

class AppColors {
  // Updated Primary Color (Red theme)
  static const Color primary = Color(0xFFEF4444); // Strong Red
  static const Color secondary = Color(0xFFFFA500);
  static const Color white = Color(0xFFFFFFFF);
  static const Color black = Color(0xFF000000);
  static const Color background = Color(0xFFF5F5F5);
  static const Color transparent = Colors.transparent;
  static const Color heading = Color(0xFF000000);
  static const Color subHeading = Color(0xFF000000);
  static const Color text = Color(0xFF000000);
  static const Color lightGrey = Color(0xFFEEEEEE);
  static const Color green = Color(0xFF3498DB);
  static const Color blue = Color(0xFF27AE60);

  // 🔴 Modern Kiosk UI Colors (Red Theme)

  // Primary Gradient Colors (Home Screen)
  static const Color gradientStart = Color(0xFFDC2626); // Dark Red
  static const Color gradientMiddle = Color(0xFFEF4444); // Main Red
  static const Color gradientEnd = Color(0xFFF87171);   // Light Red

  // Surface Colors
  static const Color surfaceLight = Color(0xFFF8F9FA);
  static const Color surfaceMedium = Color(0xFFF1F3F4);
  static const Color cardBackground = Color(0xFFFFFFFF);
  static const Color cardBorder = Color(0xFFE5E7EB);

  // Text Colors
  static const Color textPrimary = Color(0xFF111827);
  static const Color textSecondary = Color(0xFF6B7280);
  static const Color textTertiary = Color(0xFF9CA3AF);
  static const Color textOnDark = Color(0xFFFFFFFF);

  // State Colors
  static const Color success = Color(0xFF10B981);
  static const Color successLight = Color(0xFFD1FAE5);
  static const Color error = Color(0xFFEF4444);
  static const Color errorLight = Color(0xFFFEE2E2);
  static const Color warning = Color(0xFFF59E0B);
  static const Color warningLight = Color(0xFFFEF3C7);

  // Interactive Elements
  static const Color buttonPrimary = Color(0xFFEF4444);   // Red Buttons
  static const Color buttonSecondary = Color(0xFFF3F4F6);
  static const Color buttonHover = Color(0xFFDC2626);     // Darker red hover
  static const Color buttonDisabled = Color(0xFFD1D5DB);

  // Overlay Colors
  static Color get overlayLight => Colors.white.withValues(alpha: 0.15);
  static Color get overlayMedium => Colors.white.withValues(alpha: 0.25);
  static Color get overlayDark => Colors.black.withValues(alpha: 0.1);
  static Color get shadowLight => Colors.black.withValues(alpha: 0.08);
  static Color get shadowMedium => Colors.black.withValues(alpha: 0.15);

  // Category Filter Colors
  static const Color filterSelected = Color(0xFFEF4444); // Red
  static const Color filterUnselected = Color(0xFFFFFFFF);
  static const Color filterBorder = Color(0xFFD1D5DB);

  // Cart Colors
  static const Color cartBadge = Color(0xFFEF4444);
  static const Color cartEmpty = Color(0xFF9CA3AF);

  // Gradients
  static const LinearGradient primaryGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [gradientStart, gradientMiddle, gradientEnd],
  );

  static const LinearGradient cardGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [Color(0xFFFFFFFF), Color(0xFFF8F9FA)],
  );

  static const LinearGradient buttonGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [Color(0xFFEF4444), Color(0xFFDC2626)], // Red gradient for buttons
  );

  // Shadows
  static List<BoxShadow> get cardShadow => [
    BoxShadow(
      color: Colors.black.withValues(alpha: 0.08),
      offset: const Offset(0, 8),
      blurRadius: 24,
      spreadRadius: 0,
    ),
  ];

  static List<BoxShadow> get buttonShadow => [
    BoxShadow(
      color: Colors.white.withValues(alpha: 0.3),
      offset: const Offset(0, 8),
      blurRadius: 20,
      spreadRadius: 0,
    ),
  ];

  static List<BoxShadow> get elevatedShadow => [
    BoxShadow(
      color: Colors.black.withValues(alpha: 0.1),
      offset: const Offset(0, 4),
      blurRadius: 12,
      spreadRadius: 0,
    ),
  ];
}
