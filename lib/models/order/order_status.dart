import '../base.dart';

enum OrderStatus implements DomainValueBase {
  pending("637778a506b8137bbf4ff02b", "Pending", "1"),

  approved("637778ab06b8137bbf4ff02e", "Approved", "1"),
  ready("63a5dc2034cf77684064cb6c", "Ready", "1"),
  inTransit("64bb795945cd69b5df42af6c", "In Transit", "1"),

  bufferTime("67a453e3e28b8fe881cb4f45", "Buffer Time", "1"),

  delivered("64bb797745cd69b5df42af74", "Delivered", "1"),
  folio("66c9dcf299b5c41a08922ac4", "Folio", "1"),
  nonChargeable("67a0bad4e28b8fe881ca35bf", "Non Chargeable", "1"),
  completed("637778be06b8137bbf4ff037", "Completed", "1"),
  complimentary("63aa81e656d4ab1eae4bd9d3", "Complimentary", "1"),
  voided("637778c506b8137bbf4ff03a", "Void", "1"),

  notDelivered("64c2416bc7ff5d3798bd9420", "Not Delivered", "1"),
  cancelled("637778b106b8137bbf4ff031", "Cancelled", "1"),
  declined("637778b806b8137bbf4ff034", "Declined", "1");

  @override
  final String id;

  @override
  final String name;

  @override
  final String value;

  const OrderStatus(this.id, this.name, this.value);

  static const categoryId = "6377784fb2386062af7c2e22";

  static OrderStatus fromId(String id) => values.firstWhere(
        (element) => element.id == id,
    orElse: () => throw Exception('Status not found for id: $id'),
  );

  static Iterable<OrderStatus> get invalidStatuses => values.where(
        (element) => element.value == "0",
  );
}
