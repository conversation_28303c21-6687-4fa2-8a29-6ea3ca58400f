
import '../../common/types.dart';

typedef AdditionalChargeList = List<OrderAdditionalCharge>;
typedef AdditionalChargeIterable = Iterable<OrderAdditionalCharge>;

class OrderAdditionalChargeWrapper {
  final String typeId;
  String name;
  double amount;

  OrderAdditionalChargeWrapper._(this.typeId, this.name, this.amount);
}

class OrderAdditionalCharge {
  final JSONObject json;

  OrderAdditionalCharge(this.json);

  String get id => json["_id"];

  String get name => json["name"];

  double get amount => double.parse(json["value"].toString());
}
