

import '../../common/types.dart';
import '../../providers/data_provider.dart';
import '../add_on_item.dart';
import '../food/food_item.dart';

typedef OrderAddOnList = List<OrderAddOn>;
typedef OrderAddOnIterable = Iterable<OrderAddOn>;

class OrderAddOn {
  final JSONObject json;

  OrderAddOn._(this.json);

  OrderAddOn(this.json);

  OrderAddOn.create({
    required String refId,
    required double price,
    required int quantity,
    required bool defaultSelected,
    required bool mustSelected,
  }) : json = {
    "refId": refId,
    "price": price,
    "quantity": quantity,
    "defaultSelected": defaultSelected,
    "mustSelected": mustSelected,
    "disabled": false,
    "selected": true,
    "isInventory": false,
    "itemName": "",
  };

  bool get defaultSelected => json["defaultSelected"];

  bool get disabled => json["disabled"];

  String get itemName => json["itemName"];

  bool get mustSelected => json["mustSelected"];

  bool get isInventory => json["isInventory"] ?? false;

  double get price => double.parse(json["price"].toString());

  int get quantity => json["quantity"];

  String get refId => json["refId"];

  bool get selected => json["selected"];

  AddOnItem getItem(DataProvider dataProvider) {
    try {
      return dataProvider.menu.getAddOnById(refId);
    } catch (e) {
      //  ignore error
      FoodItem foodItem = dataProvider.menu.getFoodItemById(refId);
      return AddOnItem(foodItem.json);
    }
  }
}
