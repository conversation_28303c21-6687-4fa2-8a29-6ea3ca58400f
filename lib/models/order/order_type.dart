import '../base.dart';

enum OrderType implements DomainValueBase {
  dineIn("6377765206b8137bbf4feff4", "Dine In"),
  takeaway("6377765c06b8137bbf4feff7", "Take Away"),
  delivery("6377766b06b8137bbf4feffa", "Delivery");

  @override
  final String id;

  @override
  final String name;

  @override
  String get value => name;

  const OrderType(this.id, this.name);

  bool get isDineIn => this == dineIn;

  bool get isTakeaway => this == takeaway;

  static const categoryId = "63766350651df617f86bca3c";

  static OrderType fromId(String id) => values.firstWhere(
        (element) => element.id == id,
      );
}
