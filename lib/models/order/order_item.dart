import '../../common/types.dart';
import '../../providers/data_provider.dart';
import '../add_on_item.dart';
import '../food/food_item.dart';
import 'order_addon.dart';
import 'order_status.dart';

typedef OrderItemsList = List<OrderItem>;
typedef OrderItemsIterable = Iterable<OrderItem>;

class OrderItem {
  final JSONObject json;

  OrderItem(this.json);

  int get quantity => json["quantity"];

  String get id => json["_id"];

  String getName(DataProvider dataProvider) => getItem(dataProvider).name;

  String get description => json["description"] ?? "No Description";

  double get itemPrice => json["price"].toDouble();

  String? get instructions => json["kotInstructions"];

  double get price {
    double price = itemPrice;
    if (hasAddons) {
      for (OrderAddOn addon in addons) {
        price += addon.price;
      }
    }
    return price * quantity;
  }

  DateTime get createdAt => DateTime.parse(json["createdAt"]);

  double get tax => json["tax"].toDouble();

  String get inventoryId => json["inventoryId"];

  String get statusId => json["statusId"];

  OrderStatus get status => OrderStatus.fromId(statusId);

  String? get reason => json["reason"];

  FoodItem getItem(DataProvider dataProvider) {
    return dataProvider.menu.getFoodItemById(inventoryId);
  }

  int get kotNo => json["kotNumber"];

  List? get _addonsJson => json["addons"];

  bool get hasAddons => _addonsJson?.isNotEmpty ?? false;

  OrderAddOnIterable get addons {
    if (_addonsJson?.isEmpty ?? true) return [];

    OrderAddOnList addOnList = [];
    // for (JSONObject addonJson in _addonsJson!) {
    //   addOnList.add(OrderAddOn._(addonJson));
    // }
    return addOnList;
  }

  String getFormattedAddons(DataProvider dataProvider) => addons.map((e) => e.getItem(dataProvider).name).join(", ");

  String? get choiceId => json["choiceIds"]?.first;

  List<String>? get customizationIds => json["customisationIds"].cast<String>();

  Iterable<AddOnDelegate> get addonDelegates => addons.map(
        (e) => AddOnDelegate.fromOrderAddon(e),
      );

  void modifyStatus(OrderStatus status) => json["statusId"] = status.id;
}
