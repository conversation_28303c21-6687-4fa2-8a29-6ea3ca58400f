import '../common/types.dart';
import '../providers/data_provider.dart';
import './food/food_item.dart';
import 'order/order_addon.dart';

typedef AddOnList = List<AddOn>;
typedef AddOnIterable = Iterable<AddOn>;

typedef AddOnItemList = List<AddOnItem>;
typedef AddOnItemIterable = Iterable<AddOnItem>;

/// Wrapper data for List of [AddOnDelegate] in inventory (menu items)
class AddOn {
  final JSONObject json;

  AddOn(this.id, this.json);

  final int id;

  String get groupName => json["groupName"];

  /// maximum number of choices that can be selected
  int get selectionLimit {
    final value = json["selectionLimit"];
    if (value is int) return value;
    return int.parse(value);
  }

  String get addonTypeId => json["addonTypeId"];

  /// Radio or checkbox
  bool get multiSelect => json["multiSelect"];

  /// If this is true get item from inventory menu
  bool get isInventory => json["isInventory"];

  Iterable<AddOnDelegate> get items {
    List<AddOnDelegate> list = [];
    for (JSONObject itemJson in json["items"]) {
      list.add(
        AddOnDelegate._(itemJson),
      );
    }
    return list;
  }
}

/// Wrapper data for List of [AddOnItem] in [AddOn] (menu items)
class AddOnDelegate {
  final JSONObject _json;

  AddOnDelegate._(this._json);

  AddOnDelegate.fromOrderAddon(OrderAddOn addon)
      : _json = {
          "refId": addon.refId,
          "price": addon.price,
          "quantity": addon.quantity,
          "defaultSelected": addon.defaultSelected,
          "mustSelected": addon.mustSelected,
        };

  String get refId => _json["refId"];

  AddOnItem getItem(DataProvider dataProvider) {
    try {
      FoodItem item = dataProvider.menu.getFoodItemById(refId);
      return AddOnItem(item.json);
    } catch (e) {
      return dataProvider.menu.getAddOnById(refId);
    }
  }

  double get price => double.parse("${_json["price"]}");

  int get quantity {
    final value = _json["quantity"];
    if (value is int) return value;
    return int.parse(value);
  }

  bool get defaultSelected => _json["defaultSelected"];

  bool get mustSelected => _json["mustSelected"];

  Map<String, dynamic> getRequestMap(DataProvider dataProvider) => {
        "defaultSelected": defaultSelected,
        "disabled": false,
        "itemName": getItem(dataProvider).name,
        "mustSelected": mustSelected,
        "price": price,
        "quantity": quantity,
        "refId": refId,
        "selected": true,
      };
}

/// Each Add-On-Item data
class AddOnItem {
  final JSONObject _json;

  AddOnItem(this._json);

  String get id => _json["_id"];

  String get name => _json["name"];
}
