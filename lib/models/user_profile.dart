import 'dart:convert';

import '../common/types.dart';
import 'restaurant/restaurant.dart';

typedef UsersList = List<UserProfile>;
typedef UsersIterable = Iterable<UserProfile>;

const String _firstNameKey = "firstName",
    _lastNameKey = "lastName",
    _emailKey = "email",
    _phoneKey = "phone",
    _typeKey = "type",
    _permissionKey = "permissions",
    _companyKey = "company",
    _idKey = "_id";

enum UserType { guest, employee, none }

class UserProfile {
  final JSONObject json;

  final UserType type;

  static const typeRunner = "captain", typeSupervisor = "supervisor";

  static const roleValues = [typeRunner, typeSupervisor];

  UserProfile._(this.json, this.type);

  UserProfile.guest(this.json) : type = UserType.guest;

  UserProfile.employee(this.json) : type = UserType.employee;

  UserProfile.custom(String firstName, String lastName, String phone, String email)
    : json = {
        _idKey: "create_new_profile",
        _firstNameKey: firstName,
        _lastNameKey: lastName,
        _phoneKey: phone,
        _emailKey: email,
      },
      type = UserType.none;

  UserProfile.companyGuest(CompanySettings companySettings)
    : json = {
        _idKey: "create_new_profile",
        _firstNameKey: companySettings.guestFirstName,
        _lastNameKey: companySettings.guestLastName,
        _phoneKey: companySettings.guestPhone,
        _emailKey: companySettings.guestEmail,
      },
      type = UserType.none;

  factory UserProfile.parse(String jsonEncodedString) {
    Map<String, dynamic> json = jsonDecode(jsonEncodedString);
    String? typeString = json[_typeKey];

    if (typeString == null) {
      throw Exception("User type was not found");
    }

    UserType type = UserType.values.firstWhere((element) => element.toString() == typeString);
    return UserProfile._(json, type);
  }

  List? get _permissions => isEmployee ? json[_permissionKey] : null;

  String get id => json[_idKey];

  String get companyId => _permissions?.first[_companyKey][_idKey];

  String get rvcId =>
      (_permissions?.first["revenueCenters"] as List).firstWhere((element) => element["active"])[_idKey];

  Iterable<String> get roles {
    return List.from(_permissions?.first["roles"] ?? []).map((e) => e["name"].toString().toLowerCase());
  }

  String get fullName => "$firstName $lastName";

  String get firstName => json[_firstNameKey];

  String get lastName => json[_lastNameKey];

  String get email => json[_emailKey];

  String get phone => json[_phoneKey];

  bool get isEmployee => type == UserType.employee;

  bool get isGuest => type == UserType.guest;

  @override
  String toString() {
    JSONObject json = Map.from(this.json);
    json[_typeKey] = type.toString();
    return jsonEncode(json);
  }
}
