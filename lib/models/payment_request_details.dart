import 'dart:convert';

import '../../common/types.dart';

class PaymentRequestDetails {
  final JSONObject json;
  final bool _isMock;

  PaymentRequestDetails(this.json) : _isMock = false;

  PaymentRequestDetails.mock(this.json) : _isMock = true;

  String get id => json["_id"];

  String get orderId => json["orderId"];

  String get currency => json["currency"];

  String get status => json["status"];

  double get amount => json["amount"];

  String get orderToken => json["order_token"];

  Map<String, dynamic> get paymentGatewayObj => json["paymentGatewayObj"];

  @override
  String toString() => (_isMock ? "Mock: " : "Prod: ") + jsonEncode(json);
}
