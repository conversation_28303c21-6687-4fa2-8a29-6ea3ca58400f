
import '../common/types.dart';

typedef TaxList = List<Tax>;
typedef TaxIterable = Iterable<Tax>;

class Tax {
  final JSONObject json;

  Tax(this.json);

  String get id => json["_id"];

  String get name => json["name"];

  double get value {
    var val = json["value"] ?? name ?? "0.00";
    val = val.isEmpty
        ? name.isNotEmpty
            ? name
            : "0"
        : val;
    return double.parse(val);
  }
}
