import '../../common/types.dart';

abstract class DomainValueBase {
  String get id;

  String get name;

  String get value;
}

class _DomainValue {
  final String id, name, value;

  _DomainValue(JSONObject json)
      : id = json["_id"],
        name = json["name"],
        value = json["value"];
}

/// ## DomainValues
class DomainValues {
  DomainValues._();

  static String get guestSourceId => "6377795d06b8137bbf4ff054";

  static String get runnerSourceId => "6377795606b8137bbf4ff051";

  static String get companyMasterId => "6087ba5f4c5778bbb4085523";

  static void loadCurrencyValues(JSONList jsonList) {
    CurrencyValues._values.clear();
    for (JSONObject json in jsonList) {
      String id = json["_id"];
      Currency currency = Currency._(id, json["name"], json["value"]);
      CurrencyValues._values[id] = currency;
    }
  }

  static void loadDateFormatValues(JSONList jsonList) {
    DateFormatValues._values.clear();
    for (JSONObject json in jsonList) {
      _DomainValue domainValue = _DomainValue(json);
      DateFormatValues._values[domainValue.id] = domainValue.value;
    }
  }

  static void loadTimeFormatValues(JSONList jsonList) {
    TimeFormatValues._values.clear();
    for (JSONObject json in jsonList) {
      _DomainValue domainValue = _DomainValue(json);
      TimeFormatValues._values[domainValue.id] = domainValue.value;
    }
  }

  static void loadTimezoneValues(JSONList jsonList) {
    TimezoneValues._values.clear();
    for (JSONObject json in jsonList) {
      _DomainValue domainValue = _DomainValue(json);
      TimezoneValues._values[domainValue.id] = domainValue.value;
    }
  }

  static void loadFeedbackInputValues(JSONList jsonList) {
    FeedBackInputTypes._values.clear();
    for (JSONObject json in jsonList) {
      _DomainValue domainValue = _DomainValue(json);
      FeedBackInputTypes._values[domainValue.id] = domainValue.name;
    }
  }

  static void loadTableTypeCategories(JSONList jsonList) {
    TableTypeCategoryValues._values.clear();
    for (JSONObject json in jsonList) {
      _DomainValue domainValue = _DomainValue(json);
      TableTypeCategoryValues._values[domainValue.id] = domainValue.name;
    }
  }

  static void loadAdditionalChargesValues(JSONList jsonList) {
    AdditionalChargesTypes._values.clear();
    for (JSONObject json in jsonList) {
      AdditionalChargesTypes._values[json["_id"]] = json["name"];
    }
  }

  static void loadAutoChargesValues(JSONList jsonList) {
    AutoChargesValues._values.clear();
    for (JSONObject json in jsonList) {
      String id = json["_id"];
      String name = json["name"];
      String value = json["value"];
      AutoChargesValues._values[json["_id"]] = AutoChargesValues._(id, name, value);
    }
  }
}

class CurrencyValues {
  CurrencyValues._();

  static final Map<String, Currency> _values = {};

  /// Return formatted status as String
  static Currency byId(String id) => _values[id]!;
}

class Currency {
  final String id, name, symbol;

  Currency._(this.id, this.name, this.symbol);

  @override
  String toString() => "$name: $symbol";
}

class DateFormatValues {
  DateFormatValues._();

  static final Map<String, String> _values = {};

  static String byId(String id) => _values[id]!;
}

class TimeFormatValues {
  TimeFormatValues._();

  static final Map<String, String> _values = {};

  static String byId(String id) => _values[id]!;
}

class TimezoneValues {
  TimezoneValues._();

  static final Map<String, String> _values = {};

  static String byId(String id) => _values[id]!;
}

class TableTypeCategoryValues {
  TableTypeCategoryValues._();

  static final Map<String, String> _values = {};

  static String byId(String id) => _values[id] ?? "Unknown";
}

class FeedBackInputTypes {
  static const String checkboxMultiple = "Check Box Multiple",
      checkboxSingle = "Check Box Single",
      rating = "Rating",
      textBox = "Text Box";

  FeedBackInputTypes._();

  static final Map<String, String> _values = {};

  static String byId(String id) => _values[id]!;
}

class AdditionalChargesTypes {
  static const String typeTip = "64400005b9c9ea01d9cb3622";

  AdditionalChargesTypes._();

  static final Map<String, String> _values = {};

  static Map<String, String> get values => _values;

  static String? byId(String id) => _values[id];
}

class AutoChargesValues {
  final String id, name, value;

  AutoChargesValues._(this.id, this.name, this.value);

  double getCharge(double price) {
    if (value.contains("%")) {
      String percentString = value.replaceAll("%", "");
      double percent = double.parse(percentString);
      return price * percent * 0.01;
    }
    return double.parse(value);
  }

  static Iterable<AutoChargesValues> get values => _values.values;

  static final Map<String, AutoChargesValues> _values = {};

  static AutoChargesValues? byId(String id) => _values[id];
}
