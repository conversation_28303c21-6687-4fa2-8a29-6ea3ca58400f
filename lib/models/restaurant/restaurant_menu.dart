import 'package:flutter/cupertino.dart';

import '../add_on_item.dart';
import '../food/food_category.dart';
import '../food/food_item.dart';
import '../food/food_sub_category.dart';
import '../tax.dart';
import '../tip_charge.dart';

class RestaurantMenu {
  final FoodCategoriesIterable categories;

  final FoodSubCategoriesIterable subCategories;

  final _MenuItems _menuItems;

  @protected
  final Map<String, String> imagesMap;

  Iterable<FoodItem> get allFoodItems => _menuItems._itemsMap.values;

  final Map<String, Tax> _taxValues;

  Tax getTaxById(String id) => _taxValues[id] ?? Tax({"id": id, "name": "Unknown", "value": 0.0});

  final Map<String, TipCharge> _tipCharges;

  Iterable<TipCharge> get tipCharges => _tipCharges.values;

  TipCharge? getTipChargeById(String id) => _tipCharges[id];

  RestaurantMenu({
    required this.categories,
    required this.subCategories,
    required FoodItemsList foodItems,
    required AddOnItemIterable addOnItems,
    required this.imagesMap,
    required TaxIterable taxValues,
    required Iterable<TipCharge> tipCharges,
  }) : _menuItems = _MenuItems(foodItems, addOnItems),
       _taxValues = Map<String, Tax>.unmodifiable(
         () {
           Map<String, Tax> values = {};
           for (Tax tax in taxValues) {
             values[tax.id] = tax;
           }
           return values;
         }.call().cast(),
       ),
       _tipCharges = Map<String, TipCharge>.unmodifiable(
         () {
           Map<String, TipCharge> values = {};
           for (TipCharge tip in tipCharges) {
             values[tip.id] = tip;
           }
           return values;
         }.call().cast(),
       );

  FoodItemsList getItemsOfSubCategory(String id) {
    FoodItemsList foodItems = [];

    for (FoodItem foodItem in _menuItems._itemsMap.values) {
      if (foodItem.subCategoryId == id) {
        foodItems.add(foodItem);
      }
    }
    return foodItems;
  }

  FoodCategoriesIterable get nonEmptyFoodCategories =>
      categories.where((element) => hasSubCategoriesOfCategory(element.id));

  FoodSubCategoriesIterable get nonEmptySubCategories =>
      subCategories.where((element) => hasFoodItemsOfSubCategory(element.id));

  bool hasFoodItemsOfSubCategory(String subCategoryId) =>
      _menuItems._itemsMap.values.any((element) => element.subCategoryId == subCategoryId);

  bool hasSubCategoriesOfCategory(String categoryId) => subCategories.any((element) => element.parentId == categoryId);

  FoodItem getFoodItemById(String id) => _menuItems.foodItemById(id);

  AddOnItem getAddOnById(String id) => _menuItems.addOnItemById(id);

  String getImageUrlById(String id) => imagesMap[id]!;

  MenuSearchResult getResultsForQuery(String query) {
    MenuSearchResult result = MenuSearchResult();

    for (FoodSubCategory item in subCategories) {
      if (item.name.toLowerCase().contains(query)) {
        result._foodSubCategories.add(item);
      }
    }
    for (FoodItem item in _menuItems.foodItems) {
      if (item.name.toLowerCase().contains(query)) {
        result._foodItems.add(item);
      }
    }
    return result;
  }
}

class MenuSearchResult {
  final FoodSubCategoriesList _foodSubCategories = [];
  final FoodItemsList _foodItems = [];

  FoodSubCategoriesIterable get foodSubCategories => _foodSubCategories;

  FoodItemsIterable get foodItems => _foodItems;

  bool get isEmpty => foodItems.isEmpty && foodSubCategories.isEmpty;
}

class _MenuItems {
  _MenuItems(FoodItemsIterable foodItems, AddOnItemIterable addOnItems) {
    for (FoodItem item in foodItems) {
      _itemsMap[item.id] = item;
    }

    for (AddOnItem item in addOnItems) {
      _addOnItemsMap[item.id] = item;
    }
  }

  final Map<String, FoodItem> _itemsMap = {};

  final Map<String, AddOnItem> _addOnItemsMap = {};

  FoodItemsIterable get foodItems => _itemsMap.values;

  FoodItem foodItemById(String id) => _itemsMap[id]!;

  AddOnItem addOnItemById(String id) => _addOnItemsMap[id]!;
}
