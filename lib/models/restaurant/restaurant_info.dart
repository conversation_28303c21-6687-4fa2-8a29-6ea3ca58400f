import '../../../common/types.dart';

class RestaurantInfo {
  final JSONObject json;

  RestaurantInfo(this.json);

  String get name => json["name"];

  String get description => json["description"];

  String get website => json["website"];

  String get logoId => json["logoId"];

  bool get fTest => json["fTest"];

  String get objectType => json["objectType"];

  String get parentId => json["parentId"];

  DateTime get createdAt => DateTime.parse(json["createdAt"]);

  DateTime get updatedAt => DateTime.parse(json["updatedAt"]);

  int get v => json["__v"];

  String get pocId => json["pocId"];

  DateTime get nightAuditDate => DateTime.parse(json["nightAuditDate"]);

  String? logoUrl;

  @override
  String toString() => json.toString();
}
