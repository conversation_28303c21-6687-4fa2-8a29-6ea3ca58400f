import 'package:flutter/material.dart';

import '../../common/types.dart';

typedef BusinessDaysIterable = Iterable<BusinessDays>;
typedef BusinessDaysList = List<BusinessDays>;

class BusinessDays {
  final JSONObject json;

  BusinessDays(this.json);

  String get id => json["_id"];

  String get day => json["day"];

  bool get active => json["active"];

  List<BusinessTime> get timings {
    List timingsJsonList = json["timings"];
    return timingsJsonList.map((e) {
      return BusinessTime(e["startTime"], e["endTime"]);
    }).toList();
  }

  /// If time is within any of the [timings]
  bool includes(DateTime dateTime) => timings.any((element) {
        DateTime start = dateTime.copyWith(
          hour: element.start.hour,
          minute: element.start.hour,
        );

        DateTime end = dateTime.copyWith(
          hour: element.end.hour,
          minute: element.end.minute,
        );

        return dateTime.isAfter(start) && dateTime.isBefore(end);
      });
}

class BusinessTime {
  late final TimeOfDay start, end;

  BusinessTime(String startTime, String endTime) : super() {
    int start = int.parse(startTime), end = int.parse(endTime);

    this.start = TimeOfDay(
      hour: start ~/ 100,
      minute: start % 100,
    );
    this.end = TimeOfDay(
      hour: end ~/ 100,
      minute: end % 100,
    );
  }

  @override
  String toString() => "BusinessTime: $start -> $end";
}
