import '../../common/types.dart';

class Attachment {
  final JSONObject json;

  Attachment(this.json);

  String get id => json["_id"];

  String get url => json["url"];

  String get fileName => json["fileName"];

  String get originalFileName => json["originalFileName"];

  String get fileType => json["fileType"];

  int get size => json["size"];

  DateTime get createdAt => DateTime.parse(json["createdAt"]);

  DateTime get updatedAt => DateTime.parse(json["updatedAt"]);
}
