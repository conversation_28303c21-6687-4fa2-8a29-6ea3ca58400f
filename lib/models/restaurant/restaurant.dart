import 'package:flutter/material.dart';

import '../../../common/types.dart';

class Restaurant {
  final JSONObject json;

  late final RestaurantSettings restaurantSettings;
  late final PaymentSettings paymentSettings;
  late final CompanySettings companySettings;

  /// Create new restaurant object from API data
  Restaurant(
    this.json,
    String rvcId, {
    required JSONList restaurantSettings,
    required JSONList paymentSettings,
    required JSONList companySettings,
  }) {
    json["revenueCenterId"] = rvcId;
    json["settings"] = restaurantSettings;
    this.restaurantSettings = RestaurantSettings._(restaurantSettings);
    this.paymentSettings = PaymentSettings._(paymentSettings);
    this.companySettings = CompanySettings(companySettings);
  }

  /// Load from local saved preferences
  Restaurant.fromJson(this.json)
      : restaurantSettings = RestaurantSettings._(
          json["settings"].cast(),
        );

  String get companyId => json["_id"];

  String get revenueCenterId => json["revenueCenterId"];

  String get name => json["name"];

  String get description => json["description"];

  String get pocId => json["pocId"];

  JSONObject get map => json;

  @override
  String toString() => "\n__________RESTAURANT________\n"
      "companyId : $companyId\n"
      "name: $name\n"
      "revenueCenterId : $revenueCenterId\n"
      "______________________";
}

class RestaurantProfile {
  final JSONObject _json;

  RestaurantProfile(this._json);

  String get firstName => _json["firstName"];

  String get lastName => _json["lastName"];

  String? get phone => _json["phone"];

  String? get email => _json["email"];

  String get logoId => _json["logoId"];

  bool get hasValidContact => (phone != null && phone!.isNotEmpty) || (email != null && email!.isNotEmpty);
}

class RestaurantSettings {
  final List jsonList;

  late final String currencyValue, dateFormatValue, timeFormatValue, timezoneValue;

  late final int kotTimerValue;

  Duration get kotTimerDuration => Duration(minutes: kotTimerValue);

  RestaurantSettings._(this.jsonList) {
    for (JSONObject json in jsonList) {
      String name = json["name"];
      String? value = json["value"];

      if (value == null) continue;

      switch (name) {
        case "timeZone":
          timezoneValue = value;
          break;
        case "timeFormat":
          timeFormatValue = value;
          break;
        case "dateFormat":
          dateFormatValue = value;
          break;
        case "kotTimer":
          kotTimerValue = int.parse(value);
          break;
        case "currency":
          currencyValue = value;
          break;
      }
    }
  }
}

class PaymentSettings {
  final JSONList jsonList;

  late final String paymentGatewayId, key, secret;

  PaymentSettings._(this.jsonList) {
    for (Map json in jsonList) {
      switch (json["name"]) {
        case "paymentGatewayId":
          paymentGatewayId = json["value"];
          break;
        case "secret":
          secret = json["value"];
          break;
        case "key":
          key = json["value"];
          break;
      }
    }
  }

  Map get map => {
        "gatewayId": paymentGatewayId,
        "key": key,
        "secret": secret,
      };
}

class CompanySettings {
  late final String? guestFirstName, guestLastName, guestEmail, guestCountryCode, guestPhone, guestPassword;

  CompanySettings(JSONList json) : super() {
    for (JSONObject item in json) {
      String key = item["name"];
      String? value = item["value"];
      switch (key) {
        case "guestFirstName":
          guestFirstName = value;
          break;
        case "guestLastName":
          guestLastName = value;
          break;
        case "guestEmail":
          guestEmail = value;
          break;
        case "guestCountryCode":
          guestCountryCode = value;
          break;
        case "guestPhone":
          guestPhone = value;
          break;
        case "guestPassword":
          guestPassword = value;
          break;
        default:
          debugPrint("CompanySettings.CompanySettings: unimplemented (${item["name"]})");
      }
    }
  }
}
