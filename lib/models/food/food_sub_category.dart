
import '../../common/types.dart';

typedef FoodSubCategoriesList = List<FoodSubCategory>;
typedef FoodSubCategoriesIterable = Iterable<FoodSubCategory>;

class FoodSubCategory {
  final JSONObject json;

  FoodSubCategory(this.json);

  String get id => json["_id"];

  String get label => json["label"];

  String get name => json["name"];

  String get parentId => json["parentId"];

  bool get active => json["active"];

  int get displayOrder => json["displayOrder"];

  /// Use this to get image object from [DataProvider.menu]
  String? get imageId {
    String? image = json["image"];
    if (image != null && image.isNotEmpty) {
      return image;
    }
    return null;
  }
}
