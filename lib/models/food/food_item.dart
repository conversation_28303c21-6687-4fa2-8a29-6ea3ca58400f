import '../../common/types.dart';
import '../../providers/data_provider.dart';
import '../add_on_item.dart';

typedef FoodItemsList = List<FoodItem>;
typedef FoodItemsIterable = Iterable<FoodItem>;

class FoodItem {
  final JSONObject json;

  FoodItem(this.json);

  String get id => json["_id"];

  String get name => json["name"];

  String? get description => json["description"];

  double get price => double.parse("${json["price"]}");

  String get formattedPrice {
    int absolutePrice = price.toInt();
    if (price == absolutePrice) return "$absolutePrice";
    return "$price";
  }

  String get revenueCenterId => json["revenueCenterId"];

  String get categoryId => json["categoryId"];

  String get subCategoryId => json["subCategoryId"];

  int? get limitPerOrder => json["limitPerOrder"];

  int get limitPerDay => json["limitPerDay"];

  List? get _addonsJson => json["addons"];

  bool get hasAddons => _addonsJson != null && _addonsJson!.isNotEmpty;

  AddOnIterable get addons {
    if (!hasAddons) return [];

    AddOnList items = [];
    for (int i = 0; i < _addonsJson!.length; i++) {
      Map addonItemJson = _addonsJson![i];
      items.add(
        AddOn(i, addonItemJson.cast()),
      );
    }
    return items;
  }

  /// Use this to get image object from [DataProvider.menu]
  String? get imageId {
    String? image = json["imageId"];
    if (image != null && image.isNotEmpty) {
      return image;
    }
    return null;
  }

  List<String> get taxIds => (json["taxIds"] as List?)?.cast<String>() ?? [];

  num get taxPercent => json['tax']??0;

  // TaxList get taxDetails => taxIds.map((taxId) => DataProvider().menu.getTaxById(taxId)).toList();

  // double get totalTax => taxDetails.fold(0, (previousValue, tax) => previousValue + tax.value);

  // double get taxPercent => totalTax * 0.01;
}
