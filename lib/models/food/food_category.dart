
import '../../common/types.dart';

typedef FoodCategoriesList = List<FoodCategory>;
typedef FoodCategoriesIterable = Iterable<FoodCategory>;

class FoodCategory {
  final JSONObject json;

  FoodCategory(this.json);

  String get id => json["_id"];

  String get label => json["label"];

  String get name => json["name"];

  bool get active => json["active"];

  int get displayOrder => json["displayOrder"];
}
