import '../../common/types.dart';

typedef BannerImageList = List<BannerImage>;
typedef BannerImageIterable = Iterable<BannerImage>;

class BannerImage {
  static const String typeAnchor = "ANCHORTO", typeHyperLink = "HYPERLINK";

  final JSONObject json;

  BannerImage(this.json);

  String get id => json["_id"];

  String get imageId => json["imageId"];

  String? get categoryId => json["categoryId"];

  String? get subCategoryId => json["subCategoryId"];

  String get type => json["type"];

  String get subType => json["subType"];

  String? get hyperLink => json["hyperLink"];

  @override
  String toString() => json.toString();
}
