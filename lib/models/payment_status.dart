import 'base.dart';

enum PaymentStatus implements DomainValueBase {
  paid("6377790806b8137bbf4ff040", "Paid"),
  refund("6377791606b8137bbf4ff046", "Refund"),
  unpaid("6377790e06b8137bbf4ff043", "Unpaid");

  @override
  final String id;

  @override
  final String name;

  @override
  String get value => name;

  const PaymentStatus(this.id, this.name);

  static const categoryId = "637778e8b2386062af7c2e23";

  static PaymentStatus fromId(String id) => values.firstWhere(
        (element) => element.id == id,
      );
}
