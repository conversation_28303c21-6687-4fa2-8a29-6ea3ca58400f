import 'package:flutter/cupertino.dart';
import 'package:http/http.dart';

import '../common/globals.dart';

class ApiManagerBase {
  final Client client = Client();

  static void clearToken() => sharedPreferences.remove("token");


  void logRequest(BaseRequest? request, Object body) {
    debugPrint("ApiManagerBase.logRequest:"
        "\n\t\turl: ${request?.url}"
        "\n\t\tmethod: ${request?.method}"
        "\n\t\theaders: ${request?.headers}"
        "\n\t\tbody: $body");
  }

  void logResponse(Response response) {
    debugPrint("ApiManagerBase.logResponse: "
        "\n\t\tstatusCode: ${response.statusCode}"
        "\n\t\tbody: ${response.body}");
  }

  String? getToken() => sharedPreferences.getString("token");

  void saveToken(String token) {
    sharedPreferences.setString("token", token);
  }

  @mustCallSuper
  void dispose() {
    client.close();
  }
}
