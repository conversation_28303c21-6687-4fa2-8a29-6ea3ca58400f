part of 'api_manager.dart';

mixin ApiResponseValidatorMixin {
  JSONObject _getResponseJson(Response response, String message) {
    if (![200, 201].contains(response.statusCode)) {
      debugPrint("ApiResponseValidatorMixin._getResponseJson: ❌ERROR: ${response.body}");
      throw message;
    }

    JSONObject json = jsonDecode(response.body);
    if (json['code'] != 200) {
      debugPrint("ApiResponseValidatorMixin._getResponseJson: ❌ERROR: body ${response.body}");
      throw json['message'] ?? json["error"];
    }

    return json;
  }

  JSONObject validateJsonObject(
    Response response, {
    required String onResponseNotOk,
  }) {
    final json = _getResponseJson(response, onResponseNotOk);
    return JSONObject.from(json['data'] ?? {});
  }

  JSONList validateJsonList(
    Response response, {
    required String onResponseNotOk,
  }) {
    final json = _getResponseJson(response, onResponseNotOk);
    return JSONList.from(json['data'] ?? []);
  }
}
