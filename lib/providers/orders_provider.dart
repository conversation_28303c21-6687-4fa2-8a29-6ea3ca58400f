import 'package:flutter/cupertino.dart';

import '../models/order/order.dart';
import '../models/order/order_status.dart';
import 'data_provider.dart';

class OrdersProvider extends ChangeNotifier {
  OrdersProvider();

  final bool _isLoading = false;

  bool get isLoading => _isLoading;

  final Map<String, Order> _orders = {};

  Iterable<Order> get orders => _orders.values;

  OrdersIterable sortedOrders(DataProvider dataProvider) {
    OrdersIterable ordersList = List.from(_orders.values.where((element) => element.hasValidItems))
      ..sort((a, b) {
        return -1 * a.getUpdatedAt(dataProvider).compareTo(b.getUpdatedAt(dataProvider));
      });
    return ordersList;
  }

  OrdersIterable getPendingOrders(DataProvider dataProvider) =>
      List.from(sortedOrders(dataProvider).where((element) => element.hasPendingItems));
  List<OrderStatus> onGoingStatuses = [OrderStatus.approved, OrderStatus.ready];

  OrdersIterable getOngoingOrders(DataProvider dataProvider) {
    return List.from(
      sortedOrders(dataProvider).where((element) {
        return element.hasItemsOfStatus(onGoingStatuses);
      }),
    );
  }

  // All orders with status completed, void and complimentary
  OrdersIterable getCompletedOrders(DataProvider dataProvider) => List.from(
    sortedOrders(dataProvider).where((element) {
      return element.status == OrderStatus.completed ||
          element.status == OrderStatus.voided ||
          element.status == OrderStatus.complimentary ||
          element.status == OrderStatus.nonChargeable;
    }),
  );

  // Get Declined, Not Delivered and Cancelled orders
  OrdersIterable getDeclinedOrders() => List.from(
    orders.where((element) {
      return element.status == OrderStatus.declined ||
          element.status == OrderStatus.cancelled ||
          element.status == OrderStatus.notDelivered;
    }),
  );

  void updateOrder(Order order) {
    _orders[order.id] = order;
    notifyListeners();
  }

  void clearOrders() {
    _orders.clear();
    notifyListeners();
  }

  /// Updates order from backend
  ///
  /// Return order if update was successful and null otherwise
  // Future<Order?> syncOrder(Order order) async {
  //   try {
  //     ApiManager apiManager = ApiManager.tokenInstance();
  //     Order updatedOrder = await apiManager.syncOrder(order);
  //     updateOrder(updatedOrder);
  //     return updatedOrder;
  //   } catch (e) {
  //     debugPrint("OrdersView._updateOrder: ERROR: $e");
  //   }
  //   return null;
  // }

  Order? getOrderById(String id) => _orders[id];

  Map<String, int> getOrdersCountByUserId(String userId) {
    int pendingOrdersCount = 0;
    int completedOrdersCount = 0;
    int ongoingOrdersCount = 0;

    for (Order order in _orders.values) {
      if (order.runnerId == userId) {
        if (order.hasPendingItems) {
          pendingOrdersCount++;
        } else if (order.status == OrderStatus.completed) {
          completedOrdersCount++;
        } else if (order.hasItemsOfStatus(onGoingStatuses)) {
          ongoingOrdersCount++;
        }
      }
    }

    return {'pending': pendingOrdersCount, 'completed': completedOrdersCount, 'ongoing': ongoingOrdersCount};
  }
}
