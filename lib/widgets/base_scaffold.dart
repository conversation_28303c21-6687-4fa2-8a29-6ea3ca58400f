import 'package:flutter/material.dart';

class BaseScaffold extends StatelessWidget {
  final Widget child;

  const BaseScaffold({super.key, required this.child});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          Positioned.fill(
            child: Image.asset(
              'assets/images/bgpizza.jpg',
              fit: BoxFit.cover,
              alignment: Alignment.center,
            ),
          ),
          // Foreground content
          Scaffold(
            backgroundColor: Colors.transparent,
            body: child,
          ),
        ],
      ),
    );
  }
}