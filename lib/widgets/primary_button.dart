import 'package:flutter/material.dart';

import '../common/constants/colors.dart';

class PrimaryButton extends StatelessWidget {
  final VoidCallback onTap;
  final String text;
  final TextStyle textStyle;
  final EdgeInsetsGeometry padding;
  final EdgeInsetsGeometry? margin;
  final double borderRadius;
  final Color backgroundColor;
  final Color splashColor;
  final double? width;
  final double? height;

  const PrimaryButton({
    super.key,
    required this.onTap,
    required this.text,
    this.textStyle = const TextStyle(color: AppColors.white, fontSize: 24.0, fontWeight: FontWeight.w600),
    this.padding = const EdgeInsets.symmetric(vertical: 12.0, horizontal: 24.0),
    this.margin,
    this.borderRadius = 10.0,
    this.backgroundColor = Colors.green,
    this.splashColor = Colors.white54,
    this.width, // optional
    this.height,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: margin,
      width: width, // only if passed
      height: height,
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(borderRadius),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          splashColor: splashColor,
          borderRadius: BorderRadius.circular(borderRadius),
          child: Padding(
            padding: padding,
            child: Text(
              text,
              style: textStyle,
              textAlign: TextAlign.center,
            ),
          ),
        ),
      ),
    );
  }
}
